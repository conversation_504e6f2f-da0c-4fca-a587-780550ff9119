import time
import torch
from ultralytics import YOLO

#yolov8n-pose.pt 
model = YOLO("best.pt")  # load a custom model

# # Predict with the model
#results = model.train(data="data.yaml", epochs=100, imgsz=640)

# các thông số của mô hình
# <PERSON>o thời gian xử lý inference
start_time = time.time()
resultsP = model.predict(source="Data/ImgReal/frame_000284.jpg", save=True, save_txt=True)
end_time = time.time()

inference_time = end_time - start_time  # Inference time in seconds
print(f"Inference time: {end_time - start_time:.4f} seconds")

# Thông tin phần cứng
if torch.cuda.is_available():
    print("GPU Detected:")
    print(torch.cuda.get_device_name(0))
    print(f"Memory Allocated: {torch.cuda.memory_allocated(0)/1024**2:.2f} MB")
    print(f"Memory Cached: {torch.cuda.memory_reserved(0)/1024**2:.2f} MB")
else:
    print("No GPU detected, using CPU.")

#Print the prediction results
for result in resultsP:
    boxes = result.boxes  # Boxes object for bounding box outputs
    masks = result.masks  # Masks object for segmentation masks outputs
    keypoints = result.keypoints  # Keypoints object for pose outputs
    probs = result.probs  # Probs object for classification outputs
    obb = result.obb  # Oriented boxes object for OBB outputs
    #result.show()  # display to screen
    result.save(filename="result.jpg")  # save to disk

print(boxes, masks, keypoints, probs, obb)  # print results


#model.export(format="onnx")  # creates 'yolo11n.onnx'
#Tham số cơ bản: epochs=50, batch=16, imgsz=640
#Tham số tối ưu hóa: lr=0.001, momentum=0.9, weight_decay=0.0005
#Tham số điều chỉnh các thành phần loss: box=0.05, cls=0.5, pose=12, label_smoothing=0.0