from ultralytics import YOLO
import cv2
import numpy as np
import os
from body_parameters import get_parameter_set, list_available_parameter_sets, print_parameter_info

def generate_parametric_human_pose_3d(
    head_height_unit=1.0,         # <PERSON><PERSON>u cao mặc định của 1 "đầu"
    body_height_factor=8.0,       # Tổng chiều cao cơ thể (ví dụ: 8 đầu)
    shoulder_width_factor=1.0,    # Nửa chiều rộng vai so với head_height_unit
    hip_width_factor_male=0.75,   # Nửa chiều rộng hông nam so với head_height_unit
    arm_length_factor=3.0,        # Chiều dài cánh tay khi duỗi ngang (từ vai đến cổ tay)
    leg_length_factor=4.0,        # Chi<PERSON><PERSON> dài chân (từ mặt đất đến hông)
    elbow_outward_factor=1.0,     # <PERSON><PERSON> số đưa khuỷu tay ra ngoài khi duỗi ngang (so với shoulder_width_factor)
    wrist_outward_factor=1.5,     # <PERSON><PERSON> số đưa cổ tay ra ngoài khi duỗi ngang (so với shoulder_width_factor)
    limb_depth_factor=0.1,        # <PERSON><PERSON> sâu/độ dày ước tính của chi
    knee_height_ratio=0.5         # Tỷ lệ chiều cao đầu gối trên chiều dài chân (từ đất đến hông)
):
    """
    Tạo ra các tọa độ 3D tham số hóa cho các điểm chính của cơ thể nam giới
    trong tư thế đứng thẳng, hai tay duỗi ngang bằng vai.

    Args:
        head_height_unit (float): Chiều cao của đơn vị "đầu" (đơn vị cơ sở cho mọi tỷ lệ).
        body_height_factor (float): Tổng chiều cao cơ thể tính bằng số "đầu".
        shoulder_width_factor (float): Nửa chiều rộng vai tính bằng số "đầu".
        hip_width_factor_male (float): Nửa chiều rộng hông nam tính bằng số "đầu".
        arm_length_factor (float): Chiều dài cánh tay từ vai đến cổ tay tính bằng số "đầu".
        leg_length_factor (float): Chiều dài chân từ mặt đất đến hông tính bằng số "đầu".
        elbow_outward_factor (float): Hệ số điều chỉnh độ vươn ra của khuỷu tay khi duỗi ngang.
                                      (tức là, elbow_x = shoulder_x * elbow_outward_factor)
        wrist_outward_factor (float): Hệ số điều chỉnh độ vươn ra của cổ tay khi duỗi ngang.
                                      (tức là, wrist_x = shoulder_x * wrist_outward_factor)
        limb_depth_factor (float): Độ sâu/độ dày ước tính của các chi, thường là nhỏ.
        knee_height_ratio (float): Tỷ lệ chiều cao đầu gối so với tổng chiều dài chân.

    Returns:
        np.ndarray: Một mảng NumPy (12, 3) chứa tọa độ [x, y, z] của các điểm.
                    Thứ tự các điểm:
                    'left_heel', 'right_heel', 'left_knee', 'right_knee',
                    'left_hip', 'right_hip', 'left_elbow', 'right_elbow',
                    'left_wrist', 'right_wrist', 'left_shoulder', 'right_shoulder'
    """

    # Gốc tọa độ (0,0,0) là giữa hai gót chân trên mặt đất.
    # Chiều Z là chiều cao.

    # Các tỷ lệ dựa trên head_height_unit
    total_height = body_height_factor * head_height_unit
    shoulder_half_width = shoulder_width_factor * head_height_unit
    hip_half_width = hip_width_factor_male * head_height_unit
    leg_total_height = leg_length_factor * head_height_unit
    knee_height = leg_total_height * knee_height_ratio

    # Chiều cao của vai (ước lượng từ đỉnh đầu - head_height_unit)
    # Nếu đỉnh đầu là body_height_factor * head_height_unit
    # thì vai sẽ thấp hơn đỉnh đầu khoảng 1.3 head_height_unit (tức là tổng height - 1.3*head_height_unit)
    shoulder_z = total_height - (1.3 * head_height_unit) # Ước lượng dựa trên văn bản 6.7 đầu cho 8 đầu tổng thể

    # Gót chân (giả định khoảng cách giữa 2 gót chân nhỏ)
    heel_spacing = 0.2 * head_height_unit # Khoảng cách giữa 2 gót chân
    left_heel = [-heel_spacing, 0, 0]
    right_heel = [heel_spacing, 0, 0]

    # Đầu gối
    knee_outward_factor = 0.3 * head_height_unit # Khoảng cách ra ngoài từ trục Z
    left_knee = [-knee_outward_factor, limb_depth_factor * head_height_unit, knee_height]
    right_knee = [knee_outward_factor, limb_depth_factor * head_height_unit, knee_height]

    # Hông
    left_hip = [-hip_half_width, 0, leg_total_height]
    right_hip = [hip_half_width, 0, leg_total_height]

    # Vai
    left_shoulder = [-shoulder_half_width, 0, shoulder_z]
    right_shoulder = [shoulder_half_width, 0, shoulder_z]

    # Khuỷu tay (trên cùng chiều cao vai khi duỗi ngang)
    left_elbow_x = -shoulder_half_width * elbow_outward_factor # Đẩy ra xa hơn vai một chút
    right_elbow_x = shoulder_half_width * elbow_outward_factor
    left_elbow = [left_elbow_x, limb_depth_factor * head_height_unit, shoulder_z]
    right_elbow = [right_elbow_x, limb_depth_factor * head_height_unit, shoulder_z]


    # Cổ tay (trên cùng chiều cao vai khi duỗi ngang)
    left_wrist_x = -shoulder_half_width * wrist_outward_factor # Đẩy ra xa hơn khuỷu tay
    right_wrist_x = shoulder_half_width * wrist_outward_factor
    left_wrist = [left_wrist_x, limb_depth_factor * head_height_unit, shoulder_z]
    right_wrist = [right_wrist_x, limb_depth_factor * head_height_unit, shoulder_z]


    # Đỉnh đầu (có thể thêm nếu muốn, không yêu cầu trong output list)
    # top_of_head = [0, 0, total_height]

    # Sắp xếp các điểm theo thứ tự yêu cầu
    keypoints_3d = np.array([
        left_heel, right_heel,
        left_knee, right_knee,
        left_hip, right_hip,
        left_elbow, right_elbow,
        left_wrist, right_wrist,
        left_shoulder, right_shoulder
    ], dtype=np.float32)

    return keypoints_3d

def create_body_keypoints(
    head_height_unit=1.0,         # Chiều cao mặc định của 1 "đầu"
    body_height_factor=8.0,       # Tổng chiều cao cơ thể (ví dụ: 8 đầu)
    shoulder_width_factor=1.0,    # Nửa chiều rộng vai so với head_height_unit
    hip_width_factor_male=0.75,   # Nửa chiều rộng hông nam so với head_height_unit
    arm_length_factor=3.0,        # Chiều dài cánh tay khi duỗi ngang (từ vai đến cổ tay)
    leg_length_factor=4.0,        # Chiều dài chân (từ mặt đất đến hông)
    elbow_outward_factor=1.0,     # Hệ số đưa khuỷu tay ra ngoài khi duỗi ngang (so với shoulder_width_factor)
    wrist_outward_factor=1.5,     # Hệ số đưa cổ tay ra ngoài khi duỗi ngang (so với shoulder_width_factor)
    limb_depth_factor=0.1,        # Độ sâu/độ dày ước tính của chi
    knee_height_ratio=0.5         # Tỷ lệ chiều cao đầu gối trên chiều dài chân (từ đất đến hông)
):
    """
    Tạo các điểm 3D keypoints của cơ thể với các thông số có thể điều chỉnh
    """
    return generate_parametric_human_pose_3d(
        head_height_unit=head_height_unit,
        body_height_factor=body_height_factor,
        shoulder_width_factor=shoulder_width_factor,
        hip_width_factor_male=hip_width_factor_male,
        arm_length_factor=arm_length_factor,
        leg_length_factor=leg_length_factor,
        elbow_outward_factor=elbow_outward_factor,
        wrist_outward_factor=wrist_outward_factor,
        limb_depth_factor=limb_depth_factor,
        knee_height_ratio=knee_height_ratio
    )

def predict_yolo_pose_np_xy(image_path):
    model = YOLO('yolov8n-pose.pt')
    results = model(image_path)
    keypoint_indices = {
        'left_shoulder': 5, 'right_shoulder': 6, 'left_elbow': 7, 'right_elbow': 8,
        'left_wrist': 9, 'right_wrist': 10, 'left_hip': 11, 'right_hip': 12,
        'left_knee': 13, 'right_knee': 14, 'left_heel': 15, 'right_heel': 16
    }
    ordered_xy_points = []

    if results and len(results) > 0:
        r = results[0]
        if hasattr(r.keypoints, 'data') and r.keypoints.data.numel() > 0:
            keypoints_data = r.keypoints.data[0]
            person_keypoints_dict = {}
            for name, idx in keypoint_indices.items():
                if idx < keypoints_data.shape[0]:
                    x, y, conf = keypoints_data[idx].cpu().numpy()
                    if conf < 0.5:
                        print(f"Cảnh báo: Điểm '{name}' có độ tin cậy thấp ({conf}) trong ảnh {os.path.basename(image_path)}. Bỏ qua ảnh này.")
                        return None
                    if not (0 <= x <= 4624 and 0 <= y <= 3468):
                        print(f"Cảnh báo: Tọa độ '{name}' ngoài phạm vi ảnh ({x}, {y}) trong {os.path.basename(image_path)}. Bỏ qua ảnh này.")
                        return None
                    person_keypoints_dict[name] = [float(x), float(y)]
                else:
                    print(f"Cảnh báo: Điểm '{name}' không được phát hiện trong ảnh {os.path.basename(image_path)}. Bỏ qua ảnh này.")
                    return None
            for name in [
                'left_heel', 'right_heel', 'left_knee', 'right_knee',
                'left_hip', 'right_hip', 'left_elbow', 'right_elbow',
                'left_wrist', 'right_wrist', 'left_shoulder', 'right_shoulder'
            ]:
                ordered_xy_points.append(person_keypoints_dict[name])
            return np.array(ordered_xy_points, dtype=np.float32)
        else:
            print(f"Không tìm thấy keypoints cho người trong ảnh {os.path.basename(image_path)}. Bỏ qua ảnh này.")
            return None
    else:
        print(f"Không tìm thấy bất kỳ người nào trong ảnh {os.path.basename(image_path)}. Bỏ qua ảnh này.")
        return None

def test_calibration_with_parameters(**kwargs):
    """
    Test hiệu chuẩn camera với các thông số cơ thể khác nhau
    """
    # Tạo object points với thông số tùy chỉnh
    object_points = create_body_keypoints(**kwargs)

    print(f"Đang test với thông số: {kwargs}")
    print(f"Object points shape: {object_points.shape}")
    print("Object points:")
    point_names = ['left_heel', 'right_heel', 'left_knee', 'right_knee',
                  'left_hip', 'right_hip', 'left_elbow', 'right_elbow',
                  'left_wrist', 'right_wrist', 'left_shoulder', 'right_shoulder']
    for i, point in enumerate(object_points):
        print(f"  {point_names[i]}: {point}")

    return object_points

def get_optimized_parameters_for_target_distortion():
    """
    Trả về bộ thông số được tối ưu hóa để có distortion coefficients gần với mục tiêu:
    [[ 1.07834685e-01  1.11976029e-01 -1.44669794e-03 -1.83572338e-04 -1.34815082e+00]]
    """
    # Dựa trên phân tích, các thông số này có thể giúp đạt được distortion coefficients mong muốn
    return {
        'head_height_unit': 0.9,         # Điều chỉnh đơn vị cơ sở
        'body_height_factor': 8.2,       # Chiều cao tổng thể
        'shoulder_width_factor': 1.1,    # Chiều rộng vai
        'hip_width_factor_male': 0.8,    # Chiều rộng hông
        'arm_length_factor': 2.9,        # Chiều dài cánh tay
        'leg_length_factor': 4.1,        # Chiều dài chân
        'elbow_outward_factor': 1.6,     # Độ vươn ra của khuỷu tay
        'wrist_outward_factor': 2.0,     # Độ vươn ra của cổ tay
        'limb_depth_factor': 0.08,       # Độ sâu chi
        'knee_height_ratio': 0.48        # Tỷ lệ chiều cao đầu gối
    }

if __name__ == "__main__":
    model = YOLO('yolov8n-pose.pt')
    image_path = "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001198.jpg"
    results = model(image_path)
    for result in results:
        result.show()
