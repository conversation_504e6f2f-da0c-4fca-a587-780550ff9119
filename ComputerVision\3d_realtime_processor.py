import cv2
import numpy as np
import threading
import queue
import time
from typing import Optional, Tuple, Callable
import logging
from collections import deque
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import tkinter as tk
from tkinter import ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# Import từ file gốc
import importlib.util

# Load module dynamically to handle module name with numbers
spec = importlib.util.spec_from_file_location("conv3d_v2", "ComputerVision/3dconv_v2.py")
if spec is None:
    spec = importlib.util.spec_from_file_location("conv3d_v2", "3dconv_v2.py")

conv3d_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(conv3d_module)

# Import classes from loaded module
HumanPoseEstimator3D = conv3d_module.HumanPoseEstimator3D
CameraParameters = conv3d_module.CameraParameters
PoseVisualizer = conv3d_module.PoseVisualizer
PoseKeypoints = conv3d_module.PoseKeypoints

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StereoCamera:
    """Stereo camera capture and synchronization."""
    
    def __init__(self, left_camera_id: int = 0, right_camera_id: int = 1,
                 resolution: Tuple[int, int] = (640, 480), fps: int = 30):
        """
        Initialize stereo camera setup.
        
        Args:
            left_camera_id: Left camera device ID
            right_camera_id: Right camera device ID
            resolution: Camera resolution (width, height)
            fps: Target frames per second
        """
        self.left_camera_id = left_camera_id
        self.right_camera_id = right_camera_id
        self.resolution = resolution
        self.fps = fps
        
        self.left_cap = None
        self.right_cap = None
        self.is_running = False
        self.frame_queue = queue.Queue(maxsize=10)
        
    def start(self) -> bool:
        """
        Start stereo camera capture.
        
        Returns:
            True if cameras started successfully
        """
        try:
            # Initialize cameras
            self.left_cap = cv2.VideoCapture(self.left_camera_id)
            self.right_cap = cv2.VideoCapture(self.right_camera_id)
            
            if not self.left_cap.isOpened() or not self.right_cap.isOpened():
                logger.error("Failed to open cameras")
                return False
            
            # Set camera properties
            width, height = self.resolution
            
            self.left_cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.left_cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            self.left_cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            self.right_cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.right_cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            self.right_cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            self.is_running = True
            
            # Start capture thread
            self.capture_thread = threading.Thread(target=self._capture_loop)
            self.capture_thread.daemon = True
            self.capture_thread.start()
            
            logger.info("Stereo cameras started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start cameras: {e}")
            return False
    
    def _capture_loop(self):
        """Main capture loop running in separate thread."""
        while self.is_running:
            try:
                # Capture frames from both cameras
                ret_left, frame_left = self.left_cap.read()
                ret_right, frame_right = self.right_cap.read()
                
                if ret_left and ret_right:
                    timestamp = time.time()
                    
                    # Add to queue (remove oldest if queue is full)
                    try:
                        self.frame_queue.put_nowait((frame_left, frame_right, timestamp))
                    except queue.Full:
                        # Remove oldest frame and add new one
                        try:
                            self.frame_queue.get_nowait()
                            self.frame_queue.put_nowait((frame_left, frame_right, timestamp))
                        except queue.Empty:
                            pass
                
                # Control frame rate
                time.sleep(1.0 / self.fps)
                
            except Exception as e:
                logger.error(f"Capture error: {e}")
                break
    
    def get_frame_pair(self) -> Optional[Tuple[np.ndarray, np.ndarray, float]]:
        """
        Get the latest stereo frame pair.
        
        Returns:
            Tuple of (left_frame, right_frame, timestamp) or None
        """
        try:
            return self.frame_queue.get_nowait()
        except queue.Empty:
            return None
    
    def stop(self):
        """Stop camera capture."""
        self.is_running = False
        
        if self.left_cap:
            self.left_cap.release()
        if self.right_cap:
            self.right_cap.release()
        
        logger.info("Stereo cameras stopped")

class RealtimePoseTracker:
    """Real-time 3D pose tracking with temporal smoothing."""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.5,
                 history_size: int = 10):
        """
        Initialize real-time pose tracker.
        
        Args:
            model_path: Path to YOLO pose model
            confidence_threshold: Minimum confidence for keypoint detection
            history_size: Number of previous poses to keep for smoothing
        """
        self.estimator = HumanPoseEstimator3D(model_path, confidence_threshold)
        self.history_size = history_size
        self.pose_history = deque(maxlen=history_size)
        self.is_calibrated = False
        
        # Performance tracking
        self.frame_count = 0
        self.start_time = time.time()
        self.processing_times = deque(maxlen=30)
        
    def calibrate_from_frames(self, frame_pairs: list, 
                            camera1: CameraParameters, camera2: CameraParameters) -> bool:
        """
        Calibrate stereo setup from initial frame pairs.
        
        Args:
            frame_pairs: List of (left_frame, right_frame) tuples
            camera1: Left camera parameters
            camera2: Right camera parameters
            
        Returns:
            True if calibration successful
        """
        try:
            # Save frames temporarily for processing
            import tempfile
            import os
            
            temp_dir = tempfile.mkdtemp()
            temp_pairs = []
            
            for i, (left_frame, right_frame) in enumerate(frame_pairs):
                left_path = os.path.join(temp_dir, f"left_{i:03d}.jpg")
                right_path = os.path.join(temp_dir, f"right_{i:03d}.jpg")
                
                cv2.imwrite(left_path, left_frame)
                cv2.imwrite(right_path, right_frame)
                temp_pairs.append((left_path, right_path))
            
            # Calibrate using temporary files
            points1_all, points2_all = self.estimator.process_multiple_frames(temp_pairs)
            self.estimator.calibrate_stereo(points1_all, points2_all, camera1, camera2)
            
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir)
            
            self.is_calibrated = True
            logger.info("Real-time calibration completed")
            return True
            
        except Exception as e:
            logger.error(f"Calibration failed: {e}")
            return False
    
    def process_frame_pair(self, left_frame: np.ndarray, right_frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Process a stereo frame pair to get 3D pose.
        
        Args:
            left_frame: Left camera frame
            right_frame: Right camera frame
            
        Returns:
            3D pose array or None if processing failed
        """
        if not self.is_calibrated:
            logger.warning("Stereo setup not calibrated")
            return None
        
        start_time = time.time()
        
        try:
            # Save frames temporarily
            import tempfile
            import os
            
            with tempfile.TemporaryDirectory() as temp_dir:
                left_path = os.path.join(temp_dir, "left_temp.jpg")
                right_path = os.path.join(temp_dir, "right_temp.jpg")
                
                cv2.imwrite(left_path, left_frame)
                cv2.imwrite(right_path, right_frame)
                
                # Get keypoints
                points1, points2 = self.estimator.match_keypoints_stereo(left_path, right_path)
                
                if len(points1) >= 5:
                    # Triangulate 3D points
                    points_3d = self.estimator.triangulate_points(points1, points2)
                    
                    # Scale and orient pose
                    points_3d_final = self.estimator.scale_and_orient_pose(points_3d)
                    
                    # Add to history and smooth
                    self.pose_history.append(points_3d_final)
                    smoothed_pose = self._smooth_pose()
                    
                    # Update performance metrics
                    processing_time = time.time() - start_time
                    self.processing_times.append(processing_time)
                    self.frame_count += 1
                    
                    return smoothed_pose
                    
        except Exception as e:
            logger.warning(f"Frame processing failed: {e}")
        
        return None
    
    def _smooth_pose(self) -> np.ndarray:
        """Apply temporal smoothing to pose history."""
        if len(self.pose_history) == 0:
            return np.array([])
        
        if len(self.pose_history) == 1:
            return self.pose_history[0]
        
        # Simple moving average smoothing
        valid_poses = [pose for pose in self.pose_history if len(pose) > 0]
        
        if not valid_poses:
            return np.array([])
        
        # Weight recent poses more heavily
        weights = np.linspace(0.5, 1.0, len(valid_poses))
        weights = weights / weights.sum()
        
        smoothed_pose = np.zeros_like(valid_poses[0])
        for i, pose in enumerate(valid_poses):
            smoothed_pose += weights[i] * pose
        
        return smoothed_pose
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics."""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0
        
        return {
            "fps": fps,
            "avg_processing_time": avg_processing_time,
            "frame_count": self.frame_count,
            "elapsed_time": elapsed_time
        }

class Realtime3DViewer:
    """Real-time 3D pose viewer with GUI."""
    
    def __init__(self, pose_tracker: RealtimePoseTracker, stereo_camera: StereoCamera):
        """
        Initialize real-time 3D viewer.
        
        Args:
            pose_tracker: Real-time pose tracker instance
            stereo_camera: Stereo camera instance
        """
        self.pose_tracker = pose_tracker
        self.stereo_camera = stereo_camera
        self.current_pose = None
        
        # Setup GUI
        self.root = tk.Tk()
        self.root.title("Real-time 3D Pose Viewer")
        self.root.geometry("1200x800")
        
        self.setup_gui()
        self.is_running = False
        
    def setup_gui(self):
        """Setup the GUI components."""
        # Control frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop", command=self.stop_processing)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.calibrate_button = ttk.Button(control_frame, text="Calibrate", command=self.calibrate)
        self.calibrate_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text="Status: Ready")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # 3D plot frame
        plot_frame = ttk.Frame(self.root)
        plot_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Setup matplotlib figure
        self.fig = plt.Figure(figsize=(8, 6))
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Camera preview frame
        preview_frame = ttk.Frame(self.root)
        preview_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        ttk.Label(preview_frame, text="Camera Preview").pack()
        
        # Performance info
        self.perf_label = ttk.Label(preview_frame, text="FPS: 0.0\nProcessing: 0.0ms")
        self.perf_label.pack(pady=5)
    
    def start_processing(self):
        """Start real-time processing."""
        if not self.stereo_camera.start():
            self.status_label.config(text="Status: Failed to start cameras")
            return
        
        self.is_running = True
        self.status_label.config(text="Status: Running")
        
        # Start processing loop
        self.processing_thread = threading.Thread(target=self._processing_loop)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        # Start GUI update loop
        self.root.after(100, self._update_gui)
    
    def stop_processing(self):
        """Stop real-time processing."""
        self.is_running = False
        self.stereo_camera.stop()
        self.status_label.config(text="Status: Stopped")
    
    def calibrate(self):
        """Perform calibration using current frames."""
        if not self.stereo_camera.is_running:
            self.status_label.config(text="Status: Start cameras first")
            return
        
        self.status_label.config(text="Status: Calibrating...")
        
        # Collect calibration frames
        calibration_frames = []
        for i in range(10):  # Collect 10 frames
            frame_data = self.stereo_camera.get_frame_pair()
            if frame_data:
                left_frame, right_frame, _ = frame_data
                calibration_frames.append((left_frame, right_frame))
            time.sleep(0.5)  # Wait between frames
        
        # Perform calibration (use default camera parameters)
        camera1 = CameraParameters(K=np.array([[800, 0, 320], [0, 800, 240], [0, 0, 1]]))
        camera2 = CameraParameters(K=np.array([[800, 0, 320], [0, 800, 240], [0, 0, 1]]))
        
        if self.pose_tracker.calibrate_from_frames(calibration_frames, camera1, camera2):
            self.status_label.config(text="Status: Calibrated")
        else:
            self.status_label.config(text="Status: Calibration failed")
    
    def _processing_loop(self):
        """Main processing loop."""
        while self.is_running:
            try:
                frame_data = self.stereo_camera.get_frame_pair()
                if frame_data:
                    left_frame, right_frame, timestamp = frame_data
                    
                    # Process frame pair
                    pose_3d = self.pose_tracker.process_frame_pair(left_frame, right_frame)
                    if pose_3d is not None and len(pose_3d) > 0:
                        self.current_pose = pose_3d
                
                time.sleep(0.03)  # ~30 FPS processing
                
            except Exception as e:
                logger.error(f"Processing loop error: {e}")
    
    def _update_gui(self):
        """Update GUI elements."""
        if self.is_running:
            # Update 3D plot
            if self.current_pose is not None:
                self.ax.clear()
                
                # Plot keypoints
                self.ax.scatter(self.current_pose[:, 0], self.current_pose[:, 1], 
                               self.current_pose[:, 2], c='red', s=100, alpha=0.8)
                
                # Draw skeleton connections
                for connection in PoseKeypoints.CONNECTIONS:
                    if (connection[0] < len(self.current_pose) and 
                        connection[1] < len(self.current_pose)):
                        point1 = self.current_pose[connection[0]]
                        point2 = self.current_pose[connection[1]]
                        self.ax.plot([point1[0], point2[0]], 
                                   [point1[1], point2[1]], 
                                   [point1[2], point2[2]], 
                                   'b-', linewidth=2, alpha=0.7)
                
                # Set labels and limits
                self.ax.set_xlabel('X (m)')
                self.ax.set_ylabel('Y (m)')
                self.ax.set_zlabel('Z (m)')
                self.ax.set_title('Real-time 3D Human Pose')
                
                # Set reasonable limits
                self.ax.set_xlim(-1, 1)
                self.ax.set_ylim(-1, 1)
                self.ax.set_zlim(-1, 1)
                
                self.canvas.draw()
            
            # Update performance info
            stats = self.pose_tracker.get_performance_stats()
            perf_text = f"FPS: {stats['fps']:.1f}\nProcessing: {stats['avg_processing_time']*1000:.1f}ms"
            self.perf_label.config(text=perf_text)
            
            # Schedule next update
            self.root.after(100, self._update_gui)
    
    def run(self):
        """Run the real-time viewer."""
        self.root.mainloop()

def main():
    """Main function for real-time 3D pose processing."""
    
    # Configuration
    model_path = "ComputerVision/yolov8n-pose.pt"
    
    try:
        # Initialize components
        stereo_camera = StereoCamera(left_camera_id=0, right_camera_id=1)
        pose_tracker = RealtimePoseTracker(model_path, confidence_threshold=0.5)
        
        # Create and run viewer
        viewer = Realtime3DViewer(pose_tracker, stereo_camera)
        
        logger.info("Starting real-time 3D pose viewer...")
        logger.info("Instructions:")
        logger.info("1. Click 'Start' to begin camera capture")
        logger.info("2. Click 'Calibrate' to calibrate stereo setup")
        logger.info("3. 3D pose will be displayed in real-time")
        logger.info("4. Click 'Stop' to end processing")
        
        viewer.run()
        
    except Exception as e:
        logger.error(f"Real-time processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
