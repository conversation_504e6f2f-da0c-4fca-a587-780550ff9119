import cv2
import numpy as np
import os
from pathlib import Path
from typing import List, Tuple, Optional
import logging
from tqdm import tqdm
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation, PillowWriter
import tempfile
import shutil

# Import từ file gốc
import sys
import importlib.util

# Load module dynamically to handle module name with numbers
spec = importlib.util.spec_from_file_location("conv3d_v2", "ComputerVision/3dconv_v2.py")
if spec is None:
    spec = importlib.util.spec_from_file_location("conv3d_v2", "3dconv_v2.py")

conv3d_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(conv3d_module)

# Import classes from loaded module
HumanPoseEstimator3D = conv3d_module.HumanPoseEstimator3D
CameraParameters = conv3d_module.CameraParameters
PoseVisualizer = conv3d_module.PoseVisualizer
PoseKeypoints = conv3d_module.PoseKeypoints

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoFrameExtractor:
    """Trích xuất và đồng bộ frames từ stereo videos."""
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        Initialize video frame extractor.
        
        Args:
            temp_dir: Temporary directory for extracted frames
        """
        self.temp_dir = temp_dir or tempfile.mkdtemp()
        self.left_frames_dir = os.path.join(self.temp_dir, "left_frames")
        self.right_frames_dir = os.path.join(self.temp_dir, "right_frames")
        
        # Create directories
        os.makedirs(self.left_frames_dir, exist_ok=True)
        os.makedirs(self.right_frames_dir, exist_ok=True)
    
    def extract_frames(self, video_path: str, output_dir: str, 
                      frame_skip: int = 1, max_frames: Optional[int] = None) -> List[str]:
        """
        Extract frames from video.
        
        Args:
            video_path: Path to input video
            output_dir: Directory to save frames
            frame_skip: Skip every N frames (1 = extract all)
            max_frames: Maximum number of frames to extract
            
        Returns:
            List of extracted frame paths
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        logger.info(f"Video: {total_frames} frames, {fps:.2f} FPS")
        
        frame_paths = []
        frame_count = 0
        extracted_count = 0
        
        with tqdm(total=min(total_frames//frame_skip, max_frames or float('inf')), 
                 desc="Extracting frames") as pbar:
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_skip == 0:
                    frame_filename = f"frame_{extracted_count:06d}.jpg"
                    frame_path = os.path.join(output_dir, frame_filename)
                    
                    cv2.imwrite(frame_path, frame)
                    frame_paths.append(frame_path)
                    extracted_count += 1
                    pbar.update(1)
                    
                    if max_frames and extracted_count >= max_frames:
                        break
                
                frame_count += 1
        
        cap.release()
        logger.info(f"Extracted {len(frame_paths)} frames from {video_path}")
        return frame_paths
    
    def extract_stereo_frames(self, left_video: str, right_video: str,
                            frame_skip: int = 1, max_frames: Optional[int] = None) -> Tuple[List[str], List[str]]:
        """
        Extract synchronized frames from stereo videos.
        
        Args:
            left_video: Path to left camera video
            right_video: Path to right camera video
            frame_skip: Skip every N frames
            max_frames: Maximum frames to extract
            
        Returns:
            Tuple of (left_frame_paths, right_frame_paths)
        """
        logger.info("Extracting frames from stereo videos...")
        
        left_frames = self.extract_frames(left_video, self.left_frames_dir, 
                                        frame_skip, max_frames)
        right_frames = self.extract_frames(right_video, self.right_frames_dir, 
                                         frame_skip, max_frames)
        
        # Ensure same number of frames
        min_frames = min(len(left_frames), len(right_frames))
        left_frames = left_frames[:min_frames]
        right_frames = right_frames[:min_frames]
        
        logger.info(f"Synchronized {min_frames} stereo frame pairs")
        return left_frames, right_frames
    
    def cleanup(self):
        """Clean up temporary directories."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info("Cleaned up temporary directories")

class Video3DProcessor:
    """Xử lý video stereo để tạo animation 3D."""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.5):
        """
        Initialize video 3D processor.
        
        Args:
            model_path: Path to YOLO pose model
            confidence_threshold: Minimum confidence for keypoint detection
        """
        self.estimator = HumanPoseEstimator3D(model_path, confidence_threshold)
        self.frame_extractor = VideoFrameExtractor()
        self.poses_3d_sequence = []
        
    def process_stereo_videos(self, left_video: str, right_video: str,
                            camera1: CameraParameters, camera2: CameraParameters,
                            frame_skip: int = 5, max_frames: Optional[int] = None) -> List[np.ndarray]:
        """
        Process stereo videos to extract 3D pose sequence.
        
        Args:
            left_video: Path to left camera video
            right_video: Path to right camera video  
            camera1: Left camera parameters
            camera2: Right camera parameters
            frame_skip: Process every N frames
            max_frames: Maximum frames to process
            
        Returns:
            List of 3D pose arrays for each frame
        """
        try:
            # Extract frames
            left_frames, right_frames = self.frame_extractor.extract_stereo_frames(
                left_video, right_video, frame_skip, max_frames
            )
            
            # Create frame pairs for calibration (use subset)
            calibration_pairs = list(zip(left_frames[::10], right_frames[::10]))[:10]
            
            logger.info("Calibrating stereo setup...")
            points1_all, points2_all = self.estimator.process_multiple_frames(calibration_pairs)
            stereo_setup = self.estimator.calibrate_stereo(points1_all, points2_all, camera1, camera2)
            
            # Process all frame pairs
            poses_3d_sequence = []
            
            with tqdm(zip(left_frames, right_frames), 
                     total=len(left_frames), desc="Processing frames") as pbar:
                
                for left_frame, right_frame in pbar:
                    try:
                        # Get keypoints for current frame
                        points1, points2 = self.estimator.match_keypoints_stereo(left_frame, right_frame)
                        
                        if len(points1) >= 5:  # Minimum keypoints required
                            # Triangulate 3D points
                            points_3d = self.estimator.triangulate_points(points1, points2)
                            
                            # Scale and orient pose
                            points_3d_final = self.estimator.scale_and_orient_pose(points_3d)
                            poses_3d_sequence.append(points_3d_final)
                        else:
                            # Use previous pose if available, otherwise skip
                            if poses_3d_sequence:
                                poses_3d_sequence.append(poses_3d_sequence[-1].copy())
                            else:
                                poses_3d_sequence.append(np.array([]))
                                
                    except Exception as e:
                        logger.warning(f"Failed to process frame pair: {e}")
                        # Use previous pose or empty array
                        if poses_3d_sequence:
                            poses_3d_sequence.append(poses_3d_sequence[-1].copy())
                        else:
                            poses_3d_sequence.append(np.array([]))
            
            self.poses_3d_sequence = poses_3d_sequence
            logger.info(f"Processed {len(poses_3d_sequence)} frames")
            
            return poses_3d_sequence
            
        finally:
            self.frame_extractor.cleanup()
    
    def smooth_trajectories(self, poses_sequence: List[np.ndarray], 
                          window_size: int = 5) -> List[np.ndarray]:
        """
        Smooth 3D pose trajectories using moving average.
        
        Args:
            poses_sequence: List of 3D pose arrays
            window_size: Size of smoothing window
            
        Returns:
            Smoothed pose sequence
        """
        if not poses_sequence or window_size <= 1:
            return poses_sequence
        
        smoothed_poses = []
        
        for i in range(len(poses_sequence)):
            if len(poses_sequence[i]) == 0:
                smoothed_poses.append(poses_sequence[i])
                continue
                
            # Get window indices
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(poses_sequence), i + window_size // 2 + 1)
            
            # Collect valid poses in window
            window_poses = []
            for j in range(start_idx, end_idx):
                if len(poses_sequence[j]) > 0:
                    window_poses.append(poses_sequence[j])
            
            if window_poses:
                # Average poses in window
                smoothed_pose = np.mean(window_poses, axis=0)
                smoothed_poses.append(smoothed_pose)
            else:
                smoothed_poses.append(poses_sequence[i])
        
        logger.info(f"Smoothed {len(smoothed_poses)} poses")
        return smoothed_poses

class Video3DAnimator:
    """Tạo animation 3D từ sequence poses."""

    def __init__(self, figsize: Tuple[int, int] = (12, 9)):
        """
        Initialize 3D animator.

        Args:
            figsize: Figure size for animation
        """
        self.figsize = figsize

    def create_3d_animation(self, poses_sequence: List[np.ndarray],
                          output_path: str, fps: int = 30,
                          view_angles: List[Tuple[int, int]] = None) -> str:
        """
        Create 3D animation from pose sequence.

        Args:
            poses_sequence: List of 3D pose arrays
            output_path: Output video file path
            fps: Frames per second
            view_angles: List of (elevation, azimuth) angles for rotating view

        Returns:
            Path to created animation file
        """
        if not poses_sequence:
            raise ValueError("Empty pose sequence")

        # Filter valid poses
        valid_poses = [pose for pose in poses_sequence if len(pose) > 0]
        if not valid_poses:
            raise ValueError("No valid poses in sequence")

        # Setup figure and axis
        fig = plt.figure(figsize=self.figsize)
        ax = fig.add_subplot(111, projection='3d')

        # Calculate global bounds
        all_points = np.vstack(valid_poses)
        max_range = np.array([
            all_points[:, 0].max() - all_points[:, 0].min(),
            all_points[:, 1].max() - all_points[:, 1].min(),
            all_points[:, 2].max() - all_points[:, 2].min()
        ]).max() / 2.0

        mid_x = (all_points[:, 0].max() + all_points[:, 0].min()) * 0.5
        mid_y = (all_points[:, 1].max() + all_points[:, 1].min()) * 0.5
        mid_z = (all_points[:, 2].max() + all_points[:, 2].min()) * 0.5

        # Setup view angles for rotation
        if view_angles is None:
            view_angles = [(30, angle) for angle in range(0, 360, 2)]

        def animate(frame_idx):
            ax.clear()

            # Get current pose
            pose_idx = frame_idx % len(poses_sequence)
            current_pose = poses_sequence[pose_idx]

            if len(current_pose) == 0:
                return

            # Plot keypoints
            ax.scatter(current_pose[:, 0], current_pose[:, 1], current_pose[:, 2],
                      c='red', s=100, alpha=0.8)

            # Draw skeleton connections
            for connection in PoseKeypoints.CONNECTIONS:
                if (connection[0] < len(current_pose) and
                    connection[1] < len(current_pose)):
                    point1 = current_pose[connection[0]]
                    point2 = current_pose[connection[1]]
                    ax.plot([point1[0], point2[0]],
                           [point1[1], point2[1]],
                           [point1[2], point2[2]],
                           'b-', linewidth=2, alpha=0.7)

            # Set view angle (rotating camera)
            if frame_idx < len(view_angles):
                elev, azim = view_angles[frame_idx]
                ax.view_init(elev=elev, azim=azim)

            # Set labels and limits
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_title(f'3D Human Pose Animation - Frame {pose_idx}')

            # Set consistent limits
            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)

        # Create animation
        total_frames = max(len(poses_sequence), len(view_angles))
        anim = FuncAnimation(fig, animate, frames=total_frames,
                           interval=1000//fps, blit=False)

        # Save animation
        if output_path.endswith('.gif'):
            writer = PillowWriter(fps=fps)
            anim.save(output_path, writer=writer)
        else:
            anim.save(output_path, fps=fps, extra_args=['-vcodec', 'libx264'])

        plt.close(fig)
        logger.info(f"Animation saved to: {output_path}")
        return output_path

def main():
    """Main function demonstrating video 3D processing."""

    # Configuration
    model_path = "ComputerVision/yolov8n-pose.pt"

    # Camera parameters (update with your calibrated values)
    camera1 = CameraParameters(
        K=np.array([[955.64, 0, 784.75],
                   [0, 902.67, 1040.39],
                   [0, 0, 1]])
    )

    camera2 = CameraParameters(
        K=np.array([[1328.61, 0, 507.68],
                   [0, 1331.32, 951.98],
                   [0, 0, 1]])
    )

    # Video paths (update with your video files)
    left_video = "path/to/left_camera_video.mp4"
    right_video = "path/to/right_camera_video.mp4"

    try:
        # Initialize processor
        processor = Video3DProcessor(model_path, confidence_threshold=0.5)

        # Process videos
        logger.info("Processing stereo videos...")
        poses_sequence = processor.process_stereo_videos(
            left_video, right_video, camera1, camera2,
            frame_skip=5, max_frames=100  # Process every 5th frame, max 100 frames
        )

        # Smooth trajectories
        logger.info("Smoothing trajectories...")
        smoothed_poses = processor.smooth_trajectories(poses_sequence, window_size=5)

        # Create animation
        logger.info("Creating 3D animation...")
        animator = Video3DAnimator()

        # Create MP4 animation
        output_mp4 = "output_3d_animation.mp4"
        animator.create_3d_animation(smoothed_poses, output_mp4, fps=15)

        # Create GIF animation (smaller, for sharing)
        output_gif = "output_3d_animation.gif"
        animator.create_3d_animation(smoothed_poses[:30], output_gif, fps=10)  # First 30 frames

        logger.info("Video processing completed successfully!")
        logger.info(f"Outputs: {output_mp4}, {output_gif}")

    except Exception as e:
        logger.error(f"Video processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
