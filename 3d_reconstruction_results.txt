=== KẾT QUẢ TÁI DỰNG 3D VÀ THIẾT LẬP HỆ TỌA ĐỘ THẾ GIỚI ===

=== TỌA ĐỘ 3D TRONG HỆ TỌA ĐỘ THẾ GIỚI (MẶT ĐẤT Z=0) ===
 0. nose           : ( 0.091, -0.715, -0.028) m
 1. left_eye       : ( 0.102, -0.728, -0.023) m
 2. right_eye      : ( 0.080, -0.724, -0.036) m
 3. left_ear       : ( 0.049, -0.708, -0.051) m
 4. right_ear      : ( 0.126, -0.631,  0.002) m
 5. left_shoulder  : ( 0.005, -0.624, -0.066) m
 6. right_shoulder : ( 0.233, -0.639,  0.063) m
 7. left_elbow     : (-0.078, -0.610, -0.112) m
 8. right_elbow    : ( 0.349, -0.663,  0.127) m
 9. left_wrist     : (-0.123, -0.595, -0.136) m
10. right_wrist    : ( 0.085, -0.371,  0.010) m
11. left_hip       : ( 0.016, -0.376, -0.030) m
12. right_hip      : ( 0.043, -0.189,  0.009) m
13. left_knee      : (-0.001, -0.190, -0.017) m
14. right_knee     : ( 0.012, -0.004,  0.000) m
15. left_ankle     : (-0.012,  0.004,  0.000) m

=== MA TRẬN CHUYỂN ĐỔI TỪ CAMERA SANG THẾ GIỚI ===
[[          1           0           0     0.30708]
 [          0           1           0    -0.37701]
 [          0           0           1    -0.66686]
 [          0           0           0           1]]

=== THÔNG SỐ CƠ THỂ THỰC TẾ ===
total_height: 0.127 m
shoulder_width: 0.262 m
left_arm_length: 0.149 m
right_arm_length: 0.311 m
left_leg_length: 0.383 m
hip_height: -0.011 m
shoulder_height: -0.002 m

=== PHÂN TÍCH ===
- Hệ tọa độ thế giới được thiết lập với mặt đất làm tham chiếu (Z=0)
- Gốc tọa độ nằm ở trung tâm giữa hai chân
- Trục Z hướng lên trên (ngược với trọng lực)
- Tất cả các thông số được tính toán dựa trên tọa độ 3D thực tế
