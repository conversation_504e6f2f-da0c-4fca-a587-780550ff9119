import cv2
import numpy as np
import os
import glob
from pathlib import Path
from typing import List, Tuple, Optional, Dict
import logging
from tqdm import tqdm
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation, PillowWriter
import json
import pickle

# Import từ file gốc
import importlib.util

# Load module dynamically to handle module name with numbers
spec = importlib.util.spec_from_file_location("conv3d_v2", "D:\\AI\\CameraCalib\\ComputerVision\\3dconv_v2.py")
if spec is None:
    spec = importlib.util.spec_from_file_location("conv3d_v2", "3dconv_v2.py")

conv3d_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(conv3d_module)

# Import classes from loaded module
HumanPoseEstimator3D = conv3d_module.HumanPoseEstimator3D
CameraParameters = conv3d_module.CameraParameters
PoseVisualizer = conv3d_module.PoseVisualizer
PoseKeypoints = conv3d_module.PoseKeypoints

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageFolderProcessor:
    """Xử lý batch images từ folders để tạo 3D poses."""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.5):
        """
        Initialize batch image processor.
        
        Args:
            model_path: Path to YOLO pose model
            confidence_threshold: Minimum confidence for keypoint detection
        """
        self.estimator = HumanPoseEstimator3D(model_path, confidence_threshold)
        self.poses_3d_sequence = []
        
    def find_image_pairs(self, left_folder: str, right_folder: str,
                        extensions: List[str] = None) -> List[Tuple[str, str]]:
        """
        Find matching image pairs from two folders.
        
        Args:
            left_folder: Path to left camera images folder
            right_folder: Path to right camera images folder
            extensions: List of image extensions to search for
            
        Returns:
            List of (left_image_path, right_image_path) tuples
        """
        if extensions is None:
            extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        
        # Get all images from both folders
        left_images = []
        right_images = []
        
        for ext in extensions:
            left_images.extend(glob.glob(os.path.join(left_folder, ext)))
            left_images.extend(glob.glob(os.path.join(left_folder, ext.upper())))
            right_images.extend(glob.glob(os.path.join(right_folder, ext)))
            right_images.extend(glob.glob(os.path.join(right_folder, ext.upper())))
        
        # Sort images by filename
        left_images.sort()
        right_images.sort()
        
        logger.info(f"Found {len(left_images)} left images, {len(right_images)} right images")
        
        # Match images by filename or index
        image_pairs = []
        
        # Method 1: Match by filename similarity
        left_basenames = [os.path.splitext(os.path.basename(img))[0] for img in left_images]
        right_basenames = [os.path.splitext(os.path.basename(img))[0] for img in right_images]
        
        for i, left_base in enumerate(left_basenames):
            # Find matching right image
            for j, right_base in enumerate(right_basenames):
                if self._is_matching_pair(left_base, right_base):
                    image_pairs.append((left_images[i], right_images[j]))
                    break
        
        # Method 2: If no matches found, pair by index
        if not image_pairs:
            min_count = min(len(left_images), len(right_images))
            image_pairs = list(zip(left_images[:min_count], right_images[:min_count]))
        
        logger.info(f"Matched {len(image_pairs)} image pairs")
        return image_pairs
    
    def _is_matching_pair(self, left_name: str, right_name: str) -> bool:
        """
        Check if two image names are matching pairs.
        
        Args:
            left_name: Left image basename
            right_name: Right image basename
            
        Returns:
            True if images are matching pairs
        """
        # Remove common prefixes/suffixes that indicate camera side
        left_clean = left_name.lower()
        right_clean = right_name.lower()
        
        # Remove camera indicators
        camera_indicators = ['left', 'right', 'l', 'r', 'cam1', 'cam2', 'camera1', 'camera2']
        for indicator in camera_indicators:
            left_clean = left_clean.replace(indicator, '')
            right_clean = right_clean.replace(indicator, '')
        
        # Remove common separators
        separators = ['_', '-', '.']
        for sep in separators:
            left_clean = left_clean.replace(sep, '')
            right_clean = right_clean.replace(sep, '')
        
        # Check if remaining parts are similar
        return left_clean == right_clean
    
    def process_image_folders(self, left_folder: str, right_folder: str,
                            camera1: CameraParameters, camera2: CameraParameters,
                            max_images: Optional[int] = None,
                            save_intermediate: bool = True) -> List[np.ndarray]:
        """
        Process image folders to extract 3D pose sequence.
        
        Args:
            left_folder: Path to left camera images folder
            right_folder: Path to right camera images folder
            camera1: Left camera parameters
            camera2: Right camera parameters
            max_images: Maximum number of image pairs to process
            save_intermediate: Whether to save intermediate results
            
        Returns:
            List of 3D pose arrays for each image pair
        """
        # Find image pairs
        image_pairs = self.find_image_pairs(left_folder, right_folder)
        
        if max_images:
            image_pairs = image_pairs[:max_images]
        
        if not image_pairs:
            raise ValueError("No matching image pairs found")
        
        # Use subset for calibration (every 10th image or max 20 images)
        calibration_pairs = image_pairs[::max(1, len(image_pairs)//20)][:20]
        
        logger.info("Calibrating stereo setup...")
        points1_all, points2_all = self.estimator.process_multiple_frames(calibration_pairs)
        stereo_setup = self.estimator.calibrate_stereo(points1_all, points2_all, camera1, camera2)
        
        # Process all image pairs
        poses_3d_sequence = []
        failed_pairs = []
        
        with tqdm(image_pairs, desc="Processing image pairs") as pbar:
            for i, (left_img, right_img) in enumerate(pbar):
                try:
                    # Get keypoints for current image pair
                    points1, points2 = self.estimator.match_keypoints_stereo(left_img, right_img)
                    
                    if len(points1) >= 5:  # Minimum keypoints required
                        # Triangulate 3D points
                        points_3d = self.estimator.triangulate_points(points1, points2)
                        
                        # Scale and orient pose
                        points_3d_final = self.estimator.scale_and_orient_pose(points_3d)
                        poses_3d_sequence.append(points_3d_final)
                        
                        pbar.set_postfix({"Valid poses": len(poses_3d_sequence)})
                    else:
                        # Use previous pose if available
                        if poses_3d_sequence:
                            poses_3d_sequence.append(poses_3d_sequence[-1].copy())
                        else:
                            poses_3d_sequence.append(np.array([]))
                        failed_pairs.append((i, left_img, right_img))
                        
                except Exception as e:
                    logger.warning(f"Failed to process pair {i}: {e}")
                    # Use previous pose or empty array
                    if poses_3d_sequence:
                        poses_3d_sequence.append(poses_3d_sequence[-1].copy())
                    else:
                        poses_3d_sequence.append(np.array([]))
                    failed_pairs.append((i, left_img, right_img))
        
        self.poses_3d_sequence = poses_3d_sequence
        
        logger.info(f"Processed {len(poses_3d_sequence)} image pairs")
        logger.info(f"Failed pairs: {len(failed_pairs)}")
        
        # Save intermediate results if requested
        if save_intermediate:
            self.save_results(poses_3d_sequence, "batch_processing_results.pkl")
            self.save_failed_pairs(failed_pairs, "failed_pairs.json")
        
        return poses_3d_sequence
    
    def save_results(self, poses_sequence: List[np.ndarray], filename: str):
        """Save processing results to file."""
        try:
            with open(filename, 'wb') as f:
                pickle.dump(poses_sequence, f)
            logger.info(f"Results saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save results: {e}")
    
    def load_results(self, filename: str) -> List[np.ndarray]:
        """Load processing results from file."""
        try:
            with open(filename, 'rb') as f:
                poses_sequence = pickle.load(f)
            logger.info(f"Results loaded from {filename}")
            return poses_sequence
        except Exception as e:
            logger.error(f"Failed to load results: {e}")
            return []
    
    def save_failed_pairs(self, failed_pairs: List[Tuple], filename: str):
        """Save failed image pairs information."""
        try:
            failed_info = [{"index": idx, "left": left, "right": right} 
                          for idx, left, right in failed_pairs]
            with open(filename, 'w') as f:
                json.dump(failed_info, f, indent=2)
            logger.info(f"Failed pairs info saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save failed pairs info: {e}")

class Batch3DVisualizer:
    """Visualization utilities for batch processed 3D poses."""
    
    @staticmethod
    def create_pose_grid(poses_sequence: List[np.ndarray], 
                        grid_size: Tuple[int, int] = (4, 4),
                        figsize: Tuple[int, int] = (16, 12)) -> None:
        """
        Create a grid visualization of multiple 3D poses.
        
        Args:
            poses_sequence: List of 3D pose arrays
            grid_size: (rows, cols) for the grid
            figsize: Figure size
        """
        rows, cols = grid_size
        total_plots = rows * cols
        
        # Filter valid poses
        valid_poses = [pose for pose in poses_sequence if len(pose) > 0]
        
        if not valid_poses:
            logger.warning("No valid poses to visualize")
            return
        
        # Select poses to display
        step = max(1, len(valid_poses) // total_plots)
        selected_poses = valid_poses[::step][:total_plots]
        
        fig = plt.figure(figsize=figsize)
        
        for i, pose in enumerate(selected_poses):
            ax = fig.add_subplot(rows, cols, i+1, projection='3d')
            
            # Plot keypoints
            ax.scatter(pose[:, 0], pose[:, 1], pose[:, 2], 
                      c='red', s=50, alpha=0.8)
            
            # Draw skeleton connections
            for connection in PoseKeypoints.CONNECTIONS:
                if (connection[0] < len(pose) and connection[1] < len(pose)):
                    point1 = pose[connection[0]]
                    point2 = pose[connection[1]]
                    ax.plot([point1[0], point2[0]], 
                           [point1[1], point2[1]], 
                           [point1[2], point2[2]], 
                           'b-', linewidth=1, alpha=0.7)
            
            # Set labels and title
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
            ax.set_title(f'Pose {i*step}')
            
            # Set equal aspect ratio
            max_range = np.array([pose[:, 0].max() - pose[:, 0].min(),
                                 pose[:, 1].max() - pose[:, 1].min(),
                                 pose[:, 2].max() - pose[:, 2].min()]).max() / 2.0
            
            mid_x = (pose[:, 0].max() + pose[:, 0].min()) * 0.5
            mid_y = (pose[:, 1].max() + pose[:, 1].min()) * 0.5
            mid_z = (pose[:, 2].max() + pose[:, 2].min()) * 0.5
            
            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def create_batch_animation(poses_sequence: List[np.ndarray], 
                             output_path: str, fps: int = 10) -> str:
        """
        Create animation from batch processed poses.
        
        Args:
            poses_sequence: List of 3D pose arrays
            output_path: Output animation file path
            fps: Frames per second
            
        Returns:
            Path to created animation file
        """
        # Filter valid poses
        valid_poses = [pose for pose in poses_sequence if len(pose) > 0]
        
        if not valid_poses:
            raise ValueError("No valid poses to animate")
        
        # Setup figure
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # Calculate global bounds
        all_points = np.vstack(valid_poses)
        max_range = np.array([
            all_points[:, 0].max() - all_points[:, 0].min(),
            all_points[:, 1].max() - all_points[:, 1].min(),
            all_points[:, 2].max() - all_points[:, 2].min()
        ]).max() / 2.0
        
        mid_x = (all_points[:, 0].max() + all_points[:, 0].min()) * 0.5
        mid_y = (all_points[:, 1].max() + all_points[:, 1].min()) * 0.5
        mid_z = (all_points[:, 2].max() + all_points[:, 2].min()) * 0.5
        
        def animate(frame_idx):
            ax.clear()
            
            if frame_idx < len(valid_poses):
                current_pose = valid_poses[frame_idx]
                
                # Plot keypoints
                ax.scatter(current_pose[:, 0], current_pose[:, 1], current_pose[:, 2], 
                          c='red', s=100, alpha=0.8)
                
                # Draw skeleton connections
                for connection in PoseKeypoints.CONNECTIONS:
                    if (connection[0] < len(current_pose) and 
                        connection[1] < len(current_pose)):
                        point1 = current_pose[connection[0]]
                        point2 = current_pose[connection[1]]
                        ax.plot([point1[0], point2[0]], 
                               [point1[1], point2[1]], 
                               [point1[2], point2[2]], 
                               'b-', linewidth=2, alpha=0.7)
            
            # Set labels and limits
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_title(f'Batch 3D Pose Animation - Frame {frame_idx}')
            
            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # Create animation
        anim = FuncAnimation(fig, animate, frames=len(valid_poses), 
                           interval=1000//fps, blit=False)
        
        # Save animation
        if output_path.endswith('.gif'):
            writer = PillowWriter(fps=fps)
            anim.save(output_path, writer=writer)
        else:
            anim.save(output_path, fps=fps, extra_args=['-vcodec', 'libx264'])
        
        plt.close(fig)
        logger.info(f"Batch animation saved to: {output_path}")
        return output_path

def main():
    """Main function demonstrating batch image processing."""

    # Configuration
    model_path = "ComputerVision/yolov8n-pose.pt"

    # Camera parameters (update with your calibrated values)
    camera1 = CameraParameters(
        K=np.array([[955.64, 0, 784.75],
                   [0, 902.67, 1040.39],
                   [0, 0, 1]])
    )

    camera2 = CameraParameters(
        K=np.array([[1328.61, 0, 507.68],
                   [0, 1331.32, 951.98],
                   [0, 0, 1]])
    )

    # Image folders (update with your folder paths)
    left_folder = "D:\\AI\\CameraCalib\\human_frame_and"
    right_folder = "D:\\AI\\CameraCalib\\human_frame_ip"

    # Example with existing folders in the project
    # left_folder = "human_frame_and"
    # right_folder = "human_frame_ip"

    try:
        # Initialize processor
        processor = ImageFolderProcessor(model_path, confidence_threshold=0.5)

        # Process image folders
        logger.info("Processing image folders...")
        poses_sequence = processor.process_image_folders(
            left_folder, right_folder, camera1, camera2,
            max_images=50,  # Process max 50 image pairs
            save_intermediate=True
        )

        # Create visualizations
        logger.info("Creating visualizations...")

        # 1. Grid visualization of selected poses
        Batch3DVisualizer.create_pose_grid(poses_sequence, grid_size=(3, 4))

        # 2. Create animation
        output_gif = "batch_3d_animation.gif"
        Batch3DVisualizer.create_batch_animation(poses_sequence, output_gif, fps=5)

        # 3. Save individual pose plots
        valid_poses = [pose for pose in poses_sequence if len(pose) > 0]
        if valid_poses:
            # Plot first, middle, and last poses
            indices = [0, len(valid_poses)//2, len(valid_poses)-1]
            for i, idx in enumerate(indices):
                if idx < len(valid_poses):
                    PoseVisualizer.plot_3d_pose(
                        valid_poses[idx],
                        f"Batch Processed 3D Pose - Sample {i+1}"
                    )

        logger.info("Batch processing completed successfully!")
        logger.info(f"Processed {len(poses_sequence)} image pairs")
        logger.info(f"Valid poses: {len(valid_poses)}")
        logger.info(f"Animation saved to: {output_gif}")

        # Print statistics
        if valid_poses:
            logger.info("Pose Statistics:")
            all_points = np.vstack(valid_poses)
            logger.info(f"  Total 3D points: {len(all_points)}")
            logger.info(f"  X range: {all_points[:, 0].min():.3f} to {all_points[:, 0].max():.3f}")
            logger.info(f"  Y range: {all_points[:, 1].min():.3f} to {all_points[:, 1].max():.3f}")
            logger.info(f"  Z range: {all_points[:, 2].min():.3f} to {all_points[:, 2].max():.3f}")

    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
