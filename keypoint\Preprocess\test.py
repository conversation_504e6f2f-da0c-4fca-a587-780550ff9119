from ultralytics import YOLO
import csv
import cv2
import time
import os

# <PERSON><PERSON><PERSON> lư<PERSON> kết quả vào CSV
def save_results_to_csv(file_name, results):
    with open(file_name, mode="w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Image Type", "Keypoint Index", "X", "Y", "Confidence", "Preprocess Time (ms)", "Inference Time (ms)", "Postprocess Time (ms)"])
        for result in results:
            writer.writerow(result)

# Load mô hình YOLO
model = YOLO("best.pt")  # Load custom model

# Danh sách ảnh và loại ảnh
images = {
    "Original": "datasets/images/val/IMG_4682.jpg",
    "Grayscale": "Preprocess/image/image_bw.jpg",
    "CLAHE": "Preprocess/image/clahe_img.jpg",
    "Otsu Thresholding": "Preprocess/image/otsu_img.jpg",
    "Histogram Equalization": "Preprocess/image/hist_eq.jpg",
}

# Kết quả sẽ lưu ở đây
results = []

# Xử lý từng ảnh
for image_type, image_path in images.items():
    # Bắt đầu tính thời gian tiền xử lý
    preprocess_start = time.time()
    image = cv2.imread(image_path)
    preprocess_end = time.time()

    # Thực hiện suy luận
    inference_start = time.time()
    preds = model(image_path)  # Dự đoán trên ảnh
    inference_end = time.time()

    # Hậu xử lý
    postprocess_start = time.time()
    for result in preds:
        if result.keypoints is not None:  # Kiểm tra nếu có keypoints
            keypoints_data = result.keypoints.data.cpu().numpy()  # Lấy dữ liệu keypoints
            keypoints_conf = result.keypoints.conf.cpu().numpy()  # Lấy confidence
            for i, (keypoint, conf) in enumerate(zip(keypoints_data[0], keypoints_conf[0])):
                if len(keypoint) >= 3:  # Kiểm tra nếu keypoint có đủ [x, y, confidence]
                    x, y, _ = keypoint
                    results.append([
                        image_type, i, x, y, conf,
                        (preprocess_end - preprocess_start) * 1000,  # Tiền xử lý (ms)
                        (inference_end - inference_start) * 1000,  # Suy luận (ms)
                        (time.time() - postprocess_start) * 1000   # Hậu xử lý (ms)
                    ])
        else:
            print(f"No keypoints detected in {image_type}")

# Lưu kết quả vào file CSV
output_csv_path = "Preprocess/keypoints_analysis.csv"
save_results_to_csv(output_csv_path, results)
print(f"Results saved to {output_csv_path}")
