import time
import torch
from ultralytics import YOLO

# Load the YOLO model
model = YOLO("best.pt")  # Load the trained YOLO model

# Đo thời gian xử lý inference
start_time = time.time()
resultsP = model.predict(source="Data/ImgReal/frame_000284.jpg", save=True, save_txt=True)
end_time = time.time()

inference_time = end_time - start_time  # Inference time in seconds

# Thông tin phần cứng
if torch.cuda.is_available():
    gpu_name = torch.cuda.get_device_name(0)
    memory_allocated = torch.cuda.memory_allocated(0) / 1024**2  # Convert to MB
    memory_cached = torch.cuda.memory_reserved(0) / 1024**2  # Convert to MB
else:
    gpu_name = "No GPU detected, using CPU"
    memory_allocated = 0
    memory_cached = 0

# Đánh giá model trên tập validation
metrics = model.val(split="val")  # Evaluate metrics on validation set

# Tr<PERSON><PERSON> xu<PERSON><PERSON> c<PERSON><PERSON> thông số cần thiế<PERSON> từ metrics
mAP50 = metrics.box.map50()  # Mean AP at IoU 0.5
mAP50_95 = metrics.box.map()  # Mean AP at IoU 0.5:0.95
precision = metrics.box.mp()  # Mean Precision
recall = metrics.box.mr()  # Mean Recall
inference_speed = metrics.speed['inference']  # Inference Speed (ms)
nms_time = metrics.speed['nms']  # NMS Time (ms)

# Lưu thông tin thành dictionary
data = {
    "mAP50": f"{mAP50:.3f}",
    "mAP50-95": f"{mAP50_95:.3f}",
    "Precision": f"{precision:.3f}",
    "Recall": f"{recall:.3f}",
    "Inference Speed": f"{inference_speed:.2f} ms",
    "NMS Time": f"{nms_time:.2f} ms",
    "GPU Name": gpu_name,
    "Memory Allocated (MB)": f"{memory_allocated:.2f}",
    "Memory Cached (MB)": f"{memory_cached:.2f}",
    "Inference Time (s)": f"{inference_time:.3f}"
}

# In dữ liệu ra terminal
print("\n--- Evaluation Metrics ---")
for key, value in data.items():
    print(f"{key}: {value}")
