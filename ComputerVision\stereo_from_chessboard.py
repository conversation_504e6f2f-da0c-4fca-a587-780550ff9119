import cv2
import numpy as np
import glob
from scipy.spatial.transform import Rotation
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StereoCalibrationFromChessboard:
    """Stereo calibration từ chessboard images của 2 cameras."""
    
    def __init__(self, chessboard_size=(10, 7)):
        """
        Initialize stereo calibration.
        
        Args:
            chessboard_size: (width, height) của chessboard pattern
        """
        self.chessboard_size = chessboard_size
        self.objp = self._create_object_points()
        
    def _create_object_points(self):
        """Tạo 3D object points cho chessboard."""
        objp = np.zeros((np.prod(self.chessboard_size), 3), np.float32)
        objp[:, :2] = np.indices(self.chessboard_size).T.reshape(-1, 2)
        return objp
    
    def find_chessboard_corners(self, image_folder, pattern="*.jpg"):
        """
        Tìm chessboard corners trong folder ảnh.
        
        Args:
            image_folder: Đường dẫn folder chứa ảnh
            pattern: Pattern để tìm ảnh
            
        Returns:
            Tuple of (objpoints, imgpoints, image_size)
        """
        objpoints = []  # 3D points
        imgpoints = []  # 2D points
        image_size = None
        
        # Load ảnh
        images = glob.glob(f"{image_folder}/{pattern}")
        images.sort()
        
        logger.info(f"Processing {len(images)} images from {image_folder}")
        
        for fname in images:
            img = cv2.imread(fname)
            if img is None:
                continue
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            if image_size is None:
                image_size = gray.shape[::-1]  # (width, height)
            
            # Tìm chessboard corners
            ret, corners = cv2.findChessboardCorners(gray, self.chessboard_size, None)
            
            if ret:
                objpoints.append(self.objp)
                
                # Refine corners
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners2 = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                imgpoints.append(corners2)
                
                logger.info(f"Found chessboard in {fname}")
            else:
                logger.warning(f"No chessboard found in {fname}")
        
        logger.info(f"Successfully processed {len(objpoints)} images")
        return objpoints, imgpoints, image_size
    
    def calibrate_single_camera(self, objpoints, imgpoints, image_size):
        """
        Calibrate single camera.
        
        Args:
            objpoints: 3D object points
            imgpoints: 2D image points
            image_size: Image size (width, height)
            
        Returns:
            Tuple of (ret, mtx, dist, rvecs, tvecs)
        """
        ret, mtx, dist, rvecs, tvecs = cv2.calibrateCamera(
            objpoints, imgpoints, image_size, None, None
        )
        
        logger.info(f"Camera calibration RMS error: {ret}")
        return ret, mtx, dist, rvecs, tvecs
    
    def compute_stereo_transform_from_poses(self, rvecs1, tvecs1, rvecs2, tvecs2, frame_idx=0):
        """
        Tính R,t giữa 2 cameras từ poses của cùng 1 frame chessboard.
        
        Args:
            rvecs1, tvecs1: Poses của camera 1
            rvecs2, tvecs2: Poses của camera 2
            frame_idx: Index của frame để tính
            
        Returns:
            R, t: Transform từ camera 1 sang camera 2
        """
        # Lấy pose của cùng 1 frame chessboard
        rvec1, tvec1 = rvecs1[frame_idx], tvecs1[frame_idx]
        rvec2, tvec2 = rvecs2[frame_idx], tvecs2[frame_idx]
        
        # Convert rotation vectors to matrices
        R1, _ = cv2.Rodrigues(rvec1)  # World -> Camera1
        R2, _ = cv2.Rodrigues(rvec2)  # World -> Camera2
        
        # Transform từ Camera1 -> World -> Camera2
        # P_cam2 = R2 * (R1^T * P_cam1 + R1^T * t1) + t2
        # P_cam2 = (R2 * R1^T) * P_cam1 + (R2 * R1^T * t1 + t2)
        R_stereo = R2 @ R1.T
        t_stereo = R2 @ R1.T @ tvec1 + tvec2
        
        return R_stereo, t_stereo.flatten()
    
    def compute_robust_stereo_transform(self, rvecs1, tvecs1, rvecs2, tvecs2):
        """
        Tính R,t robust từ multiple chessboard frames.
        
        Args:
            rvecs1, tvecs1: Poses của camera 1
            rvecs2, tvecs2: Poses của camera 2
            
        Returns:
            R, t: Average transform từ camera 1 sang camera 2
        """
        if len(rvecs1) != len(rvecs2):
            raise ValueError("Number of poses must be equal for both cameras")
        
        R_list = []
        t_list = []
        
        # Tính R,t cho mỗi frame
        for i in range(len(rvecs1)):
            try:
                R_i, t_i = self.compute_stereo_transform_from_poses(
                    rvecs1, tvecs1, rvecs2, tvecs2, i
                )
                R_list.append(R_i)
                t_list.append(t_i)
            except Exception as e:
                logger.warning(f"Failed to compute transform for frame {i}: {e}")
        
        if not R_list:
            raise ValueError("No valid transforms computed")
        
        # Average rotation matrices using quaternions
        R_avg = self._average_rotation_matrices(R_list)
        
        # Average translation
        t_avg = np.mean(t_list, axis=0)
        
        logger.info(f"Computed robust stereo transform from {len(R_list)} frames")
        return R_avg, t_avg
    
    def _average_rotation_matrices(self, R_list):
        """Average rotation matrices using quaternions."""
        # Convert to quaternions
        quats = []
        for R in R_list:
            rot = Rotation.from_matrix(R)
            quats.append(rot.as_quat())
        
        # Average quaternions
        quat_avg = np.mean(quats, axis=0)
        quat_avg = quat_avg / np.linalg.norm(quat_avg)  # Normalize
        
        # Convert back to rotation matrix
        rot_avg = Rotation.from_quat(quat_avg)
        return rot_avg.as_matrix()
    
    def stereo_calibrate_direct(self, objpoints1, imgpoints1, objpoints2, imgpoints2,
                               mtx1, dist1, mtx2, dist2, image_size):
        """
        Direct stereo calibration using cv2.stereoCalibrate.
        
        Args:
            objpoints1, imgpoints1: Camera 1 calibration data
            objpoints2, imgpoints2: Camera 2 calibration data
            mtx1, dist1: Camera 1 intrinsic parameters
            mtx2, dist2: Camera 2 intrinsic parameters
            image_size: Image size
            
        Returns:
            R, t, E, F: Stereo calibration results
        """
        # Ensure same number of calibration images
        min_images = min(len(objpoints1), len(objpoints2))
        objpoints1 = objpoints1[:min_images]
        imgpoints1 = imgpoints1[:min_images]
        objpoints2 = objpoints2[:min_images]
        imgpoints2 = imgpoints2[:min_images]
        
        # Stereo calibration
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.0001)
        
        ret, mtx1_new, dist1_new, mtx2_new, dist2_new, R, t, E, F = cv2.stereoCalibrate(
            objpoints1, imgpoints1, imgpoints2,
            mtx1, dist1, mtx2, dist2,
            image_size,
            criteria=criteria,
            flags=cv2.CALIB_FIX_INTRINSIC  # Fix intrinsic parameters
        )
        
        logger.info(f"Stereo calibration RMS error: {ret}")
        return R, t, E, F
    
    def validate_stereo_transform(self, R, t, objpoints, imgpoints1, imgpoints2, 
                                 mtx1, mtx2, dist1=None, dist2=None):
        """
        Validate stereo transform by reprojection error.
        
        Args:
            R, t: Stereo transform
            objpoints: 3D object points
            imgpoints1, imgpoints2: 2D image points from both cameras
            mtx1, mtx2: Camera matrices
            dist1, dist2: Distortion coefficients
            
        Returns:
            Average reprojection error
        """
        if dist1 is None:
            dist1 = np.zeros(5)
        if dist2 is None:
            dist2 = np.zeros(5)
        
        total_error = 0
        total_points = 0
        
        for i in range(len(objpoints)):
            # Project 3D points to camera 1
            imgpoints1_proj, _ = cv2.projectPoints(
                objpoints[i], np.zeros(3), np.zeros(3), mtx1, dist1
            )
            
            # Transform 3D points to camera 2 coordinate system
            objpoints_cam2 = (R @ objpoints[i].T).T + t
            
            # Project to camera 2
            imgpoints2_proj, _ = cv2.projectPoints(
                objpoints_cam2, np.zeros(3), np.zeros(3), mtx2, dist2
            )
            
            # Calculate reprojection errors
            error1 = cv2.norm(imgpoints1[i], imgpoints1_proj, cv2.NORM_L2) / len(imgpoints1_proj)
            error2 = cv2.norm(imgpoints2[i], imgpoints2_proj, cv2.NORM_L2) / len(imgpoints2_proj)
            
            total_error += error1 + error2
            total_points += 2
        
        avg_error = total_error / total_points
        logger.info(f"Average reprojection error: {avg_error}")
        return avg_error

def main():
    """Demo stereo calibration từ chessboard."""
    
    # Configuration
    chessboard_size = (10, 7)  # Adjust theo chessboard của bạn
    
    # Folders chứa ảnh chessboard (update paths)
    camera1_folder = "D:/AI/CameraCalib/frame_and"  # Left camera images
    camera2_folder = "D:/AI/CameraCalib/frame_ip"   # Right camera images
    
    try:
        # Initialize calibrator
        calibrator = StereoCalibrationFromChessboard(chessboard_size)
        
        # 1. Find chessboard corners for both cameras
        logger.info("Finding chessboard corners...")
        objpoints1, imgpoints1, image_size1 = calibrator.find_chessboard_corners(camera1_folder)
        objpoints2, imgpoints2, image_size2 = calibrator.find_chessboard_corners(camera2_folder)
        
        if not objpoints1 or not objpoints2:
            raise ValueError("No chessboard corners found")
        
        # 2. Calibrate individual cameras
        logger.info("Calibrating individual cameras...")
        ret1, mtx1, dist1, rvecs1, tvecs1 = calibrator.calibrate_single_camera(
            objpoints1, imgpoints1, image_size1
        )
        ret2, mtx2, dist2, rvecs2, tvecs2 = calibrator.calibrate_single_camera(
            objpoints2, imgpoints2, image_size2
        )
        
        print("Camera 1 Matrix:")
        print(mtx1)
        print("Camera 2 Matrix:")
        print(mtx2)
        
        # 3. Method 1: Compute stereo transform from poses
        logger.info("Computing stereo transform from poses...")
        R_poses, t_poses = calibrator.compute_robust_stereo_transform(
            rvecs1, tvecs1, rvecs2, tvecs2
        )
        
        print("\nStereo Transform (from poses):")
        print("R =")
        print(R_poses)
        print("t =")
        print(t_poses)
        
        # 4. Method 2: Direct stereo calibration
        logger.info("Direct stereo calibration...")
        R_direct, t_direct, E, F = calibrator.stereo_calibrate_direct(
            objpoints1, imgpoints1, objpoints2, imgpoints2,
            mtx1, dist1, mtx2, dist2, image_size1
        )
        
        print("\nStereo Transform (direct calibration):")
        print("R =")
        print(R_direct)
        print("t =")
        print(t_direct)
        
        # 5. Compare results
        print("\nComparison:")
        print(f"Rotation difference (Frobenius norm): {np.linalg.norm(R_poses - R_direct):.6f}")
        print(f"Translation difference (L2 norm): {np.linalg.norm(t_poses - t_direct):.6f}")
        
        # 6. Validate transforms
        logger.info("Validating transforms...")
        error_poses = calibrator.validate_stereo_transform(
            R_poses, t_poses, objpoints1, imgpoints1, imgpoints2, mtx1, mtx2, dist1, dist2
        )
        error_direct = calibrator.validate_stereo_transform(
            R_direct, t_direct, objpoints1, imgpoints1, imgpoints2, mtx1, mtx2, dist1, dist2
        )
        
        print(f"\nValidation Results:")
        print(f"Poses method reprojection error: {error_poses:.4f}")
        print(f"Direct method reprojection error: {error_direct:.4f}")
        
        # Recommend best method
        if error_poses < error_direct:
            print("\nRecommendation: Use poses method")
            print("R =", R_poses)
            print("t =", t_poses)
        else:
            print("\nRecommendation: Use direct method")
            print("R =", R_direct)
            print("t =", t_direct)
        
    except Exception as e:
        logger.error(f"Stereo calibration failed: {e}")
        raise

if __name__ == "__main__":
    main()
