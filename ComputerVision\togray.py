import glob
import os
import cv2


def convert_images_to_gray(input_path):
    # <PERSON><PERSON><PERSON> thư mục output nếu chưa tồn tại
    output_path = 'calib_images_gray'
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    
    # <PERSON><PERSON><PERSON>nh s<PERSON>ch tất cả ảnh
    images = glob.glob(os.path.join(input_path, '*.jpg'))
    
    for image_path in images:
        # Đọc ảnh
        img = cv2.imread(image_path)
        
        # <PERSON><PERSON><PERSON><PERSON> sang grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Tạo tên file output
        filename = os.path.basename(image_path)
        output_file = os.path.join(output_path, 'gray_' + filename)
        
        # Lưu ảnh grayscale
        cv2.imwrite(output_file, gray)
        print(f'Converted {filename} to grayscale')

# G<PERSON><PERSON> hàm chuyển đổi
convert_images_to_gray('calib_images')