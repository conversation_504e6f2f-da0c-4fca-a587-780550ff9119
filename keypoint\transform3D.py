import cv2
import numpy as np
import matplotlib.pyplot as plt
import subprocess
import json
from mpl_toolkits.mplot3d import Axes3D

#XỬ LÝ KEYPOINT TRONG 2 ẢNH TƯƠNG ỨNG
def process_two_keypoints(file1, file2, image_width, image_height):
    """
    Xử lý đồng thời 2 file TXT chứa keypoints, loại bỏ các tọa độ [0, 0] đồng bộ giữa 2 file.
    Args:
        file1 (str): Đường dẫn file TXT của ảnh 1.
        file2 (str): Đường dẫn file TXT của ảnh 2.
        image_width (int): Chiều rộng ảnh gốc.
        image_height (int): Chiều cao ảnh gốc.
    Returns:
        tuple: Hai mảng tọa độ keypoints đã loại bỏ đồng bộ các điểm [0, 0].
    """
    # Hàm xử lý keypoints từ một file
    def extract_keypoints(file_path):
        with open(file_path, 'r') as file:
            lines = file.readlines()
        keypoints_all = []
        for line in lines:
            data = line.strip().split()
            if len(data) < 6:  # Bỏ qua dòng không hợp lệ
                continue
            keypoints = list(map(float, data[5:]))
            points = []
            for i in range(0, len(keypoints), 3):
                kpx = keypoints[i] * image_width
                kpy = keypoints[i + 1] * image_height
                points.append([kpx, kpy])
            keypoints_all.append(points)
        return keypoints_all
    # Đọc keypoints từ 2 file
    keypoints1 = extract_keypoints(file1)[0]  # Ảnh 1
    keypoints2 = extract_keypoints(file2)[0]  # Ảnh 2
    # Loại bỏ các cặp tọa độ có giá trị [0, 0]
    filtered_keypoints1 = []
    filtered_keypoints2 = []
    for kp1, kp2 in zip(keypoints1, keypoints2):
        if kp1 != [0, 0] and kp2 != [0, 0]:  # Chỉ giữ lại các cặp hợp lệ
            filtered_keypoints1.append(kp1)
            filtered_keypoints2.append(kp2)
    return np.array(filtered_keypoints1), np.array(filtered_keypoints2)
# Đường dẫn tới file TXT của 2 ảnh
file1 = 'C:\\Users\\<USER>\\BKA\\AI\\keypoint\\runs\\pose\\predict2\\labels\\frame_000280.txt'
file2 = 'C:\\Users\\<USER>\\BKA\\AI\\keypoint\\runs\\pose\\predict3\\labels\\frame_000284.txt'
# Kích thước ảnh gốc
image_width = 640  # Thay bằng chiều rộng ảnh gốc
image_height = 640  # Thay bằng chiều cao ảnh gốc
# Gọi hàm xử lý
s1, s2 = process_two_keypoints(file1, file2, image_width, image_height)

#######
points1 = np.array(s1)
points2 = np.array(s2)

def extract_video_metadata(video_path):
    """
    Trích xuất metadata từ file video MOV sử dụng FFmpeg
    """
    try:
        # Chạy lệnh FFprobe để lấy thông tin metadata
        result = subprocess.run([
            'ffprobe', 
            '-v', 'quiet', 
            '-print_format', 'json', 
            '-show_format', 
            '-show_streams', 
            video_path
        ], capture_output=True, text=True)
        
        # Phân tích JSON metadata
        metadata = json.loads(result.stdout)
        
        # In ra toàn bộ metadata của video
        print(json.dumps(metadata, indent=2))
        
        # Trích xuất thông tin camera cụ thể
        for stream in metadata.get('streams', []):
            if stream.get('codec_type') == 'video':
                print("\nThông tin Camera:")
                print(f"Độ phân giải: {stream.get('width')}x{stream.get('height')}")
                print(f"Codec: {stream.get('codec_name')}")
                print(f"Tốc độ khung hình: {stream.get('r_frame_rate')}")
                
                # Kiểm tra các thẻ metadata bổ sung
                tags = stream.get('tags', {})
                for tag_key, tag_value in tags.items():
                    print(f"{tag_key}: {tag_value}")
        
        return metadata
    
    except Exception as e:
        print(f"Lỗi khi đọc metadata: {e}")
        return None

def estimate_camera_matrix(video_path):
    """
    Ước lượng ma trận camera từ video
    """
    # Mở video
    cap = cv2.VideoCapture(video_path)
    
    # Lấy thông số video
    width = 640
    height = 640
    # Ước lượng focal length (phương pháp đơn giản)
    # Giả sử sensor width là 5.76mm (iPhone 11 Pro)
    sensor_width = 5.76  # mm
    focal_length_mm = 4.25  # mm (iPhone 11 Pro)
    
    # Tính toán focal length pixel
    fx = width * focal_length_mm / sensor_width
    fy = height * focal_length_mm / sensor_width
    
    # Tạo ma trận camera calibration (K matrix)
    K = np.array([
        [1024.015748031496, 0, width/2],    # focal length x, center x
        [0, 576.0088582677165, height/2],   # focal length y, center y
        [0, 0, 1]
    ])
    
    print("\nƯớc lượng Ma trận Camera (K):")
    print(K)
    
    cap.release()
    return K

# Sử dụng
video_path = 'Data/a.mov' 
metadata = extract_video_metadata(video_path)
camera_matrix = estimate_camera_matrix(video_path)

def estimate_camera_pose(points1, points2):
    # Kiểm tra kích thước điểm đầu vào
    if points1.shape != points2.shape:
        raise ValueError("Kích thước points1 và points2 phải giống nhau")
    
    # Tìm ma trận Fundamental bằng RANSAC
    F, mask = cv2.findFundamentalMat(points1, points2, cv2.FM_RANSAC)
    print("Ma trận Fundamental (F):\n", F)
    
    # Ma trận camera calibration (giả sử)
    # phải thay thế bằng ma trận calibration thực tế của camera
    K = estimate_camera_matrix(video_path)
    
    # Tính ma trận Essential 
    E = K.T @ F @ K
    print("Ma trận Essential Matrix (E):\n", E)
    
    # Khôi phục ma trận xoay và vector tịnh tiến
    try:
        _, R, T, mask = cv2.recoverPose(E, points1, points2)
        print("Ma trận xoay (R):\n", R)
        print("Vector tịnh tiến (T):\n", T)
        
        # Camera 1 tại gốc tọa độ: [I | 0]
        P1 = np.hstack((np.eye(3), np.zeros((3, 1))))
        
        # Camera 2 với ma trận [R | T]
        P2 = np.hstack((R, T))
        
        # Triangulation 
        points4D = cv2.triangulatePoints(P1, P2, points1.T, points2.T)
        
        # Chuyển từ dạng thuần nhất về tọa độ 3D
        points3D = points4D[:3] / points4D[3]
        print("Tọa độ 3D của các điểm:\n", points3D.T)
        
        return points3D
    
    except Exception as e:
        print(f"Lỗi khi ước lượng pose camera: {e}")
        return None

def visualize_3d_points(points3D):
    if points3D is None:
        print("Không có điểm 3D để hiển thị")
        return
    
    # Tách tọa độ X, Y, Z
    x = points3D[0, :]
    y = points3D[1, :]
    z = points3D[2, :]
    
    # Tạo đồ thị 3D
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    
    # Vẽ điểm 3D
    ax.scatter(x, y, z, c='r', marker='o')
    
    # Thêm nhãn và tiêu đề
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title('Điểm 3D sau khi Triangulation')
    
    plt.show()

# Thực hiện ước lượng pose camera
points3D = estimate_camera_pose(points1, points2)

# Trực quan hóa các điểm 3D
visualize_3d_points(points3D)