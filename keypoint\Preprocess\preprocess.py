import cv2
import matplotlib.pyplot as plt

# Đ<PERSON><PERSON>nh
image = cv2.imread("Data/ImgReal/frame_000280.jpg")
image = cv2.resize(image, (640, 640))

# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sang grayscale
image_bw = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# <PERSON><PERSON><PERSON> mịn ảnh để giảm nhi<PERSON>u
image_bw = cv2.<PERSON><PERSON><PERSON>Blur(image_bw, (5, 5), 0)

# CLAHE
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
clahe_img = clahe.apply(image_bw)

# Thresholding
_, otsu_img = cv2.threshold(image_bw, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

# Histogram Equalization
hist_eq = cv2.equalizeHist(image_bw)

# <PERSON><PERSON><PERSON> <PERSON><PERSON> kết quả
plt.subplot(2, 2, 1)
plt.title("Original Grayscale")
plt.imshow(image_bw, cmap='gray')

plt.subplot(2, 2, 2)
plt.title("CLAHE")
plt.imshow(clahe_img, cmap='gray')

plt.subplot(2, 2, 3)
plt.title("Otsu Threshold")
plt.imshow(otsu_img, cmap='gray')

plt.subplot(2, 2, 4)
plt.title("Histogram Equalization")
plt.imshow(hist_eq, cmap='gray')

# Lưu ảnh sau xử lý
cv2.imwrite("Preprocess/image/image_bw.jpg", image_bw)
cv2.imwrite("Preprocess/image/clahe_img.jpg", clahe_img)
cv2.imwrite("Preprocess/image/otsu_img.jpg", otsu_img)
cv2.imwrite("Preprocess/image/hist_eq.jpg", hist_eq)

plt.tight_layout()
plt.show()
