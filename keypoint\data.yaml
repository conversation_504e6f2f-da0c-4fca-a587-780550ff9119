path: C:\Users\<USER>\BKA\AI\keypoint\datasets # dataset root dir
train: C:\Users\<USER>\BKA\AI\keypoint\datasets\images\train # train images (relative to 'path') 4 images
val: C:\Users\<USER>\BKA\AI\keypoint\datasets\images\val # val images (relative to 'path') 4 images
test: # test images (optional)

# Keypoints
kpt_shape: [17, 3] # number of keypoints, number of dims (2 for x,y or 3 for x,y,visible)
#flip_idx: [0, 2, 1, 4, 3, 6, 5, 8, 7, 10, 9, 12, 11, 14, 13, 16, 15]
flip_idx: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]

# Classes dictionary
names:
    0: person