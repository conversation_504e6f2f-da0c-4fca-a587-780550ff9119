import cv2
import os

def extract_frames(video_path, output_folder, start_time, end_time):
    """
    Extract frames from a video between specified start and end times and save them to a folder.

    Args:
        video_path (str): Path to the input video file (MOV format).
        output_folder (str): Path to the folder where frames will be saved.
        start_time (float): Start time in seconds.
        end_time (float): End time in seconds.

    Returns:
        None
    """
    # Create the output folder if it doesn't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Open the video file
    cap = cv2.VideoCapture(video_path)

    if not cap.isOpened():
        print("Error: Cannot open video file.")
        return

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)  # Frames per second
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps

    print(f"Video FPS: {fps}")
    print(f"Video Duration: {duration:.2f} seconds")

    # Convert start and end times to frame numbers
    start_frame = int(start_time * fps)
    end_frame = int(end_time * fps)

    if start_time > duration or end_time > duration:
        print("Error: Start or end time exceeds video duration.")
        cap.release()
        return

    if start_frame >= end_frame:
        print("Error: Start time must be less than end time.")
        cap.release()
        return

    # Set the video position to the start frame
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

    frame_count = start_frame

    while frame_count < end_frame:
        ret, frame = cap.read()
        if not ret:
            print("Error: Unable to read frame.")
            break

        # Save the frame as an image file
        frame_filename = os.path.join(output_folder, f"frame_{frame_count:06d}.jpg")
        cv2.imwrite(frame_filename, frame)

        frame_count += 1

    print(f"Frames extracted and saved to {output_folder}")

    # Release the video capture object
    cap.release()

# Example usage
video_path = "Data/b.mov"  # Path to your MOV video file
output_folder = "Data/bCorner"  # Folder to save frames
start_time = 9  # Start time in seconds
end_time = 10  # End time in seconds

extract_frames(video_path, output_folder, start_time, end_time)
#284(acorner)-280(bcorner)
