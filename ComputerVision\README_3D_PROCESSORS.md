# 3D Human Pose Processing Modules

Bộ 3 module mở rộng từ `3dconv_v2.py` để xử lý video, batch images và realtime processing.

## 📁 Cấu trúc Files

```
ComputerVision/
├── 3dconv_v2.py              # Module gốc (KHÔNG THAY ĐỔI)
├── 3d_video_processor.py     # Xử lý video stereo
├── 3d_batch_processor.py     # Xử lý batch images
├── 3d_realtime_processor.py  # Xử lý realtime
└── README_3D_PROCESSORS.md   # File này
```

## 🎥 1. Video Processor (`3d_video_processor.py`)

### Tính năng:
- Trích xuất frames từ stereo videos
- Xử lý sequence frames để tạo 3D poses
- Tạo animation 3D (MP4, GIF)
- Temporal smoothing cho trajectories

### Sử dụng:
```python
from ComputerVision.3d_video_processor import Video3DProcessor, Video3DAnimator
from ComputerVision.3dconv_v2 import CameraParameters

# Khởi tạo
processor = Video3DProcessor("yolov8n-pose.pt", confidence_threshold=0.5)

# Camera parameters
camera1 = CameraParameters(K=np.array([[955.64, 0, 784.75], [0, 902.67, 1040.39], [0, 0, 1]]))
camera2 = CameraParameters(K=np.array([[1328.61, 0, 507.68], [0, 1331.32, 951.98], [0, 0, 1]]))

# Xử lý video
poses_sequence = processor.process_stereo_videos(
    "left_video.mp4", "right_video.mp4", 
    camera1, camera2, frame_skip=5, max_frames=100
)

# Tạo animation
animator = Video3DAnimator()
animator.create_3d_animation(poses_sequence, "output.mp4", fps=15)
```

### Input:
- 2 video files (left/right camera)
- Camera parameters đã calibrated

### Output:
- MP4 animation
- GIF animation
- Sequence 3D poses

## 📸 2. Batch Processor (`3d_batch_processor.py`)

### Tính năng:
- Tự động tìm matching image pairs từ 2 folders
- Xử lý hàng loạt stereo image pairs
- Lưu intermediate results
- Tạo grid visualization và animation

### Sử dụng:
```python
from ComputerVision.3d_batch_processor import ImageFolderProcessor, Batch3DVisualizer

# Khởi tạo
processor = ImageFolderProcessor("yolov8n-pose.pt", confidence_threshold=0.5)

# Xử lý folders
poses_sequence = processor.process_image_folders(
    "left_images_folder", "right_images_folder",
    camera1, camera2, max_images=50, save_intermediate=True
)

# Tạo visualizations
Batch3DVisualizer.create_pose_grid(poses_sequence, grid_size=(3, 4))
Batch3DVisualizer.create_batch_animation(poses_sequence, "batch_animation.gif", fps=5)
```

### Input:
- 2 folders chứa stereo images
- Tự động match images theo tên file

### Output:
- Grid visualization của multiple poses
- Batch animation
- Pickle file lưu results
- JSON file lưu failed pairs

## 🎮 3. Realtime Processor (`3d_realtime_processor.py`)

### Tính năng:
- Real-time stereo camera capture
- Live 3D pose tracking với GUI
- Temporal smoothing
- Performance monitoring

### Sử dụng:
```python
from ComputerVision.3d_realtime_processor import Realtime3DViewer, RealtimePoseTracker, StereoCamera

# Khởi tạo components
stereo_camera = StereoCamera(left_camera_id=0, right_camera_id=1)
pose_tracker = RealtimePoseTracker("yolov8n-pose.pt", confidence_threshold=0.5)

# Tạo GUI viewer
viewer = Realtime3DViewer(pose_tracker, stereo_camera)
viewer.run()
```

### GUI Controls:
- **Start**: Bắt đầu camera capture
- **Calibrate**: Calibrate stereo setup từ live frames
- **Stop**: Dừng processing
- **Real-time 3D display**: Hiển thị pose trực tiếp

### Input:
- 2 USB cameras hoặc webcams
- Real-time calibration

### Output:
- Live 3D pose visualization
- Performance statistics (FPS, processing time)

## 🔧 Dependencies

Cài đặt thêm các packages:
```bash
pip install tqdm matplotlib pillow tkinter
```

## 📋 Cấu hình Camera Parameters

Cập nhật camera parameters trong mỗi file:
```python
camera1 = CameraParameters(
    K=np.array([[fx1, 0, cx1], 
               [0, fy1, cy1], 
               [0, 0, 1]])
)

camera2 = CameraParameters(
    K=np.array([[fx2, 0, cx2], 
               [0, fy2, cy2], 
               [0, 0, 1]])
)
```

## 🚀 Quick Start

### 1. Video Processing:
```bash
cd ComputerVision
python 3d_video_processor.py
```

### 2. Batch Processing:
```bash
cd ComputerVision  
python 3d_batch_processor.py
```

### 3. Realtime Processing:
```bash
cd ComputerVision
python 3d_realtime_processor.py
```

## 📊 Performance Tips

### Video Processing:
- Sử dụng `frame_skip=5` để tăng tốc
- Giới hạn `max_frames` cho test
- Chọn resolution phù hợp

### Batch Processing:
- Sử dụng `save_intermediate=True` để backup
- Xử lý theo batch nhỏ nếu memory hạn chế
- Check failed pairs để debug

### Realtime Processing:
- Đảm bảo 2 cameras có cùng resolution
- Calibrate trước khi tracking
- Monitor FPS và processing time

## 🔍 Troubleshooting

### Import Errors:
- Đảm bảo `3dconv_v2.py` trong cùng thư mục
- Check YOLO model path

### Camera Issues:
- Kiểm tra camera IDs (0, 1, 2...)
- Test cameras riêng lẻ trước
- Đảm bảo cameras không bị sử dụng bởi app khác

### Performance Issues:
- Giảm resolution cameras
- Tăng `frame_skip` cho video
- Giảm `confidence_threshold`

## 📝 Notes

- Tất cả 3 modules **TẬN DỤNG** code từ `3dconv_v2.py`
- **KHÔNG THAY ĐỔI** file gốc
- Mỗi module có thể chạy độc lập
- Kết quả tương thích với nhau

## 🎯 Use Cases

- **Video Processor**: Phân tích video đã quay sẵn
- **Batch Processor**: Xử lý dataset lớn images
- **Realtime Processor**: Demo, testing, live applications
